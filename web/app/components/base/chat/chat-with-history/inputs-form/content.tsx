import React, { memo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useChatWithHistoryContext } from '../context'
import Input from '@/app/components/base/input'
import Textarea from '@/app/components/base/textarea'
import { PortalSelect } from '@/app/components/base/select'
import { FileUploaderInAttachmentWrapper } from '@/app/components/base/file-uploader'
import { InputVarType } from '@/app/components/workflow/types'
import UserIdentifier from '../user-identifier'

type Props = {
  showTip?: boolean
}

const InputsFormContent = ({ showTip }: Props) => {
  const { t } = useTranslation()
  const {
    appParams,
    inputsForms,
    currentConversationId,
    currentConversationInputs,
    setCurrentConversationInputs,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
  } = useChatWithHistoryContext()

  // 统一使用当前输入值，不区分是否有对话ID
  const inputsFormValue = currentConversationId ? currentConversationInputs : newConversationInputs

  const handleFormChange = useCallback((variable: string, value: any) => {
    // 更新当前对话的输入值
    if (currentConversationId) {
      const updatedInputs = {
        ...currentConversationInputs,
        [variable]: value,
      }
      setCurrentConversationInputs(updatedInputs)
      // 同时更新新对话的输入值，保持同步
      handleNewConversationInputsChange({
        ...updatedInputs,
      })
    }
    else {
      // 如果没有当前对话ID，只更新新对话的输入值
      handleNewConversationInputsChange({
        ...newConversationInputsRef.current,
        [variable]: value,
      })
    }
  }, [newConversationInputsRef, handleNewConversationInputsChange, currentConversationInputs, setCurrentConversationInputs, currentConversationId])

  const visibleInputsForms = inputsForms.filter(form => form.hide !== true)

  // 处理用户ID变化
  const handleUserIdChange = useCallback((userId: string) => {
    console.log('User ID changed:', userId)
    // 用户ID变化时，可以在这里添加额外的处理逻辑
    // 比如重新初始化聊天状态、清除当前对话等
  }, [])

  return (
    <div className='space-y-4'>
      {/* 用户标识输入 */}
      <UserIdentifier
        className="mb-4"
        onUserIdChange={handleUserIdChange}
      />

      {visibleInputsForms.map(form => (
        <div key={form.variable} className='space-y-1'>
          <div className='flex h-6 items-center gap-1'>
            <div className='system-md-semibold text-text-secondary'>{form.label}</div>
            {!form.required && (
              <div className='system-xs-regular text-text-tertiary'>{t('appDebug.variableTable.optional')}</div>
            )}
          </div>
          {form.type === InputVarType.textInput && (
            <Input
              value={inputsFormValue?.[form.variable] || ''}
              onChange={e => handleFormChange(form.variable, e.target.value)}
              placeholder={form.label}
            />
          )}
          {form.type === InputVarType.number && (
            <Input
              type='number'
              value={inputsFormValue?.[form.variable] || ''}
              onChange={e => handleFormChange(form.variable, e.target.value)}
              placeholder={form.label}
            />
          )}
          {form.type === InputVarType.paragraph && (
            <Textarea
              value={inputsFormValue?.[form.variable] || ''}
              onChange={e => handleFormChange(form.variable, e.target.value)}
              placeholder={form.label}
            />
          )}
          {form.type === InputVarType.select && (
            <PortalSelect
              popupClassName='w-[200px]'
              value={inputsFormValue?.[form.variable]}
              items={form.options.map((option: string) => ({ value: option, name: option }))}
              onSelect={item => handleFormChange(form.variable, item.value as string)}
              placeholder={form.label}
            />
          )}
          {form.type === InputVarType.singleFile && (
            <FileUploaderInAttachmentWrapper
              value={inputsFormValue?.[form.variable] ? [inputsFormValue?.[form.variable]] : []}
              onChange={files => handleFormChange(form.variable, files[0])}
              fileConfig={{
                allowed_file_types: form.allowed_file_types,
                allowed_file_extensions: form.allowed_file_extensions,
                allowed_file_upload_methods: form.allowed_file_upload_methods,
                number_limits: 1,
                fileUploadConfig: (appParams as any).system_parameters,
              }}
            />
          )}
          {form.type === InputVarType.multiFiles && (
            <FileUploaderInAttachmentWrapper
              value={inputsFormValue?.[form.variable] || []}
              onChange={files => handleFormChange(form.variable, files)}
              fileConfig={{
                allowed_file_types: form.allowed_file_types,
                allowed_file_extensions: form.allowed_file_extensions,
                allowed_file_upload_methods: form.allowed_file_upload_methods,
                number_limits: form.max_length,
                fileUploadConfig: (appParams as any).system_parameters,
              }}
            />
          )}
        </div>
      ))}
      {showTip && currentConversationId && (
        <div className='system-xs-regular text-text-tertiary'>{t('share.chat.configStatusDes')}</div>
      )}
      {showTip && !currentConversationId && (
        <div className='system-xs-regular text-text-tertiary'>{t('share.chat.chatFormTip')}</div>
      )}
    </div>
  )
}

export default memo(InputsFormContent)
