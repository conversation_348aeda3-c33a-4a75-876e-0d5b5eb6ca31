from datetime import datetime, timedelta
from flask_restful import Resource, reqparse
from sqlalchemy import func, desc, and_

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import setup_required, account_initialization_required
from libs.login import login_required
from extensions.ext_database import db
from models.model import App, Conversation, Message, EndUser


class SimpleUserAnalyticsApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    def get(self, app_model: App):
        """获取简化的用户分析数据"""
        parser = reqparse.RequestParser()
        parser.add_argument('range', type=str, default='7d', location='args')
        parser.add_argument('user_id', type=str, location='args')
        parser.add_argument('start_date', type=str, location='args')
        parser.add_argument('end_date', type=str, location='args')
        args = parser.parse_args()

        # 计算时间范围
        time_range = args['range']
        user_id = args.get('user_id')
        start_date_str = args.get('start_date')
        end_date_str = args.get('end_date')
        # 处理自定义时间范围
        if start_date_str and end_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d') + timedelta(days=1)
            except ValueError:
                # 如果日期格式错误，回退到默认范围
                start_date = datetime.now() - timedelta(days=7)
                end_date = datetime.now()
        else:
            # 使用预设时间范围
            if time_range == '1d':
                start_date = datetime.now() - timedelta(days=1)
            elif time_range == '7d':
                start_date = datetime.now() - timedelta(days=7)
            elif time_range == '30d':
                start_date = datetime.now() - timedelta(days=30)
            elif time_range == '90d':
                start_date = datetime.now() - timedelta(days=90)
            else:
                start_date = datetime.now() - timedelta(days=7)

            end_date = datetime.now()
        
        try:
            # 获取总用户数
            total_users = db.session.query(EndUser).filter(
                EndUser.app_id == app_model.id
            ).count()
            
            # 获取今日活跃用户数
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            active_users_today = db.session.query(EndUser.id).join(
                Message, Message.from_end_user_id == EndUser.id
            ).filter(
                and_(
                    EndUser.app_id == app_model.id,
                    Message.created_at >= today_start
                )
            ).distinct().count()
            
            # 获取总对话数
            total_conversations = db.session.query(Conversation).filter(
                Conversation.app_id == app_model.id
            ).count()
            
            # 获取总消息数
            total_messages = db.session.query(Message).filter(
                Message.app_id == app_model.id
            ).count()
            
            # 获取用户分布数据（包含详细信息）
            user_distribution_query = db.session.query(
                EndUser.id.label('user_id'),
                EndUser.name.label('user_name'),
                EndUser.external_user_id.label('external_user_id'),
                EndUser.created_at.label('user_created_at'),
                func.count(Message.id).label('message_count'),
                func.count(func.distinct(Conversation.id)).label('conversation_count'),
                func.max(Message.created_at).label('last_active')
            ).outerjoin(
                Message, and_(
                    Message.from_end_user_id == EndUser.id,
                    Message.app_id == app_model.id,
                    Message.created_at >= start_date,
                    Message.created_at <= end_date
                )
            ).outerjoin(
                Conversation, and_(
                    Conversation.from_end_user_id == EndUser.id,
                    Conversation.app_id == app_model.id
                )
            ).filter(
                EndUser.app_id == app_model.id
            )

            # 如果指定了用户ID，添加筛选
            if user_id:
                user_distribution_query = user_distribution_query.filter(EndUser.id == user_id)

            user_distribution = user_distribution_query.group_by(
                EndUser.id, EndUser.name, EndUser.external_user_id, EndUser.created_at
            ).order_by(
                desc('message_count')
            ).limit(20).all()
            
            # 格式化用户分布数据并合并相同名称的用户
            user_name_map = {}
            for user in user_distribution:
                # 优先使用external_user_id，然后是name，最后是id
                display_name = user.external_user_id or user.user_name or f"用户{user.user_id[:8]}"

                # 如果相同名称的用户已存在，合并数据
                if display_name in user_name_map:
                    user_name_map[display_name]['message_count'] += (user.message_count or 0)
                    user_name_map[display_name]['conversation_count'] += (user.conversation_count or 0)
                    # 更新最后活跃时间为最新的
                    if user.last_active and (not user_name_map[display_name]['last_active'] or user.last_active > user_name_map[display_name]['last_active']):
                        user_name_map[display_name]['last_active'] = user.last_active
                else:
                    user_name_map[display_name] = {
                        'user_id': user.user_id,
                        'user_name': display_name,
                        'message_count': user.message_count or 0,
                        'conversation_count': user.conversation_count or 0,
                        'last_active': user.last_active.isoformat() if user.last_active else None,
                        'created_at': user.user_created_at.isoformat() if user.user_created_at else None,
                    }

            # 转换为列表并按消息数量排序
            formatted_user_distribution = list(user_name_map.values())
            # 确保last_active字段格式正确
            for user_data in formatted_user_distribution:
                if user_data['last_active'] and not isinstance(user_data['last_active'], str):
                    user_data['last_active'] = user_data['last_active'].isoformat()
            formatted_user_distribution.sort(key=lambda x: x['message_count'], reverse=True)
            
            return {
                'total_users': total_users,
                'active_users_today': active_users_today,
                'total_conversations': total_conversations,
                'total_messages': total_messages,
                'user_distribution': formatted_user_distribution,
                'daily_active_users': self._get_daily_active_users(app_model.id, start_date, end_date, user_id)
            }
            
        except Exception as e:
            # 如果查询失败，返回默认数据
            return {
                'total_users': 0,
                'active_users_today': 0,
                'total_conversations': 0,
                'total_messages': 0,
                'user_distribution': [],
                'daily_active_users': []
            }

    def _get_daily_active_users(self, app_id, start_date, end_date, user_id=None):
        """获取每日活跃用户数据"""
        try:
            # 生成日期范围
            date_range = []
            current_date = start_date.date()
            end_date_only = end_date.date()

            while current_date <= end_date_only:
                date_range.append(current_date)
                current_date += timedelta(days=1)

            daily_data = []
            for date in date_range:
                day_start = datetime.combine(date, datetime.min.time())
                day_end = datetime.combine(date, datetime.max.time())

                # 查询当天的活跃用户数
                query = db.session.query(EndUser.id).join(
                    Message, Message.from_end_user_id == EndUser.id
                ).filter(
                    and_(
                        EndUser.app_id == app_id,
                        Message.created_at >= day_start,
                        Message.created_at <= day_end
                    )
                )

                # 如果指定了用户ID，添加筛选
                if user_id:
                    query = query.filter(EndUser.id == user_id)

                user_count = query.distinct().count()

                daily_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'user_count': user_count
                })

            return daily_data
        except Exception as e:
            print(f"Error getting daily active users: {e}")
            return []


class UserListApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model
    def get(self, app_model: App):
        """获取应用的用户列表"""
        users = db.session.query(
            EndUser.id,
            EndUser.name,
            EndUser.external_user_id,
            EndUser.session_id,
            EndUser.is_anonymous,
            EndUser.created_at
        ).filter(
            EndUser.app_id == app_model.id
        ).order_by(
            desc(EndUser.created_at)
        ).all()

        user_list = []
        for user in users:
            # 优先使用name，然后external_user_id，最后使用session_id
            display_name = (
                user.name or
                user.external_user_id or
                user.session_id or
                f"User {user.id[:8]}"
            )
            user_list.append({
                "id": user.id,
                "name": display_name,
                "raw_name": user.name,
                "session_id": user.session_id,
                "external_user_id": user.external_user_id,
                "is_anonymous": user.is_anonymous,
                "created_at": user.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        return {"users": user_list}


# 注册API路由
api.add_resource(SimpleUserAnalyticsApi, '/apps/<uuid:app_id>/user-analytics')
api.add_resource(UserListApi, '/apps/<uuid:app_id>/users')
